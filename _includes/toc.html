{% capture tocWorkspace %}
    {% comment %}
        Version 1.0.6
        https://github.com/allejo/jekyll-toc

        "...like all things liquid - where there's a will, and ~36 hours to spare, there's usually a/some way" ~jaybe

        Usage:
            {% include toc.html html=content sanitize=true class="inline_toc" id="my_toc" h_min=2 h_max=3 %}

        Parameters:
            * html         (string) - the HTML of compiled markdown generated by kramdown in Jekyll

        Optional Parameters:
            * sanitize     (bool)   : false  - when set to true, the headers will be stripped of any HTML in the TOC
            * class        (string) :   ''   - a CSS class assigned to the TOC
            * id           (string) :   ''   - an ID to assigned to the TOC
            * h_min        (int)    :   1    - the minimum TOC header level to use; any header lower than this value will be ignored
            * h_max        (int)    :   6    - the maximum TOC header level to use; any header greater than this value will be ignored
            * ordered      (bool)   : false  - when set to true, an ordered list will be outputted instead of an unordered list
            * item_class   (string) :   ''   - add custom class(es) for each list item; has support for '%level%' placeholder, which is the current heading level
            * baseurl      (string) :   ''   - add a base url to the TOC links for when your TOC is on another page than the actual content
            * anchor_class (string) :   ''   - add custom class(es) for each anchor element

        Output:
            An ordered or unordered list representing the table of contents of a markdown block. This snippet will only
            generate the table of contents and will NOT output the markdown given to it
    {% endcomment %}

    {% capture my_toc %}{% endcapture %}
    {% assign orderedList = include.ordered | default: false %}
    {% assign minHeader = include.h_min | default: 1 %}
    {% assign maxHeader = include.h_max | default: 6 %}
    {% assign nodes = include.html | split: '<h' %}
    {% assign firstHeader = true %}

    {% capture listModifier %}{% if orderedList %}1.{% else %}-{% endif %}{% endcapture %}

    {% for node in nodes %}
        {% if node == "" %}
            {% continue %}
        {% endif %}

        {% assign headerLevel = node | replace: '"', '' | slice: 0, 1 | times: 1 %}

        {% if headerLevel < minHeader or headerLevel > maxHeader %}
            {% continue %}
        {% endif %}

        {% if firstHeader %}
            {% assign firstHeader = false %}
            {% assign minHeader = headerLevel %}
        {% endif %}

        {% assign indentAmount = headerLevel | minus: minHeader | add: 1 %}
        {% assign _workspace = node | split: '</h' %}

        {% assign _idWorkspace = _workspace[0] | split: 'id="' %}
        {% assign _idWorkspace = _idWorkspace[1] | split: '"' %}
        {% assign html_id = _idWorkspace[0] %}

        {% capture _hAttrToStrip %}{{ _workspace[0] | split: '>' | first }}>{% endcapture %}
        {% assign header = _workspace[0] | replace: _hAttrToStrip, '' %}

        {% assign space = '' %}
        {% for i in (1..indentAmount) %}
            {% assign space = space | prepend: '    ' %}
        {% endfor %}

        {% unless include.item_class == blank %}
            {% capture listItemClass %}{:.{{ include.item_class | replace: '%level%', headerLevel }}}{% endcapture %}
        {% endunless %}

        {% capture my_toc %}{{ my_toc }}
{{ space }}{{ listModifier }} {{ listItemClass }} [{% if include.sanitize %}{{ header | strip_html }}{% else %}{{ header }}{% endif %}]({% if include.baseurl %}{{ include.baseurl }}{% endif %}#{{ html_id }}){% if include.anchor_class %}{:.{{ include.anchor_class }}}{% endif %}{% endcapture %}
    {% endfor %}

    {% if include.class %}
        {% capture my_toc %}{:.{{ include.class }}}
{{ my_toc | lstrip }}{% endcapture %}
    {% endif %}

    {% if include.id %}
        {% capture my_toc %}{: #{{ include.id }}}
{{ my_toc | lstrip }}{% endcapture %}
    {% endif %}
{% endcapture %}{% assign tocWorkspace = '' %}{{ my_toc | markdownify | strip }}