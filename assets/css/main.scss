---
# Add a comment to make this file sass-y.
# Change this file for any custom CSS.
---

/* We need to add display:inline in order to align the '>>' of the 'read more' link */
.post-excerpt p {
	display:inline;
}

/* Custom styles for Current Projects page */
.current-projects-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.projects-header {
    background: linear-gradient(135deg, #00ab6b, #038252);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0, 171, 107, 0.2);
}

.projects-header .date-range {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.projects-header .last-updated {
    font-size: 1.1rem;
    opacity: 0.9;
}

.projects-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.day-section {
    border-left: 4px solid #00ab6b;
    padding-left: 2rem;
    margin-bottom: 2rem;
}

.day-header {
    margin-bottom: 2rem;
}

.day-title {
    color: #00ab6b;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #00ab6b;
    display: inline-block;
}

.project-date {
    background: #f8f9fa;
    color: #038252;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    display: inline-block;
    margin-bottom: 0.8rem;
    border: 1px solid #e3edf3;
}

.project-item {
    background: #fff;
    border: 1px solid #e3edf3;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.project-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.project-title {
    color: #00ab6b;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    border-bottom: 2px solid #00ab6b;
    padding-bottom: 0.5rem;
}

.project-description {
    line-height: 1.6;
    color: #333;
}

.project-description p {
    margin-bottom: 1rem;
}

.project-image {
    margin: 1.5rem 0;
    text-align: center;
}

.project-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.project-media {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e3edf3;
}

.project-media h4 {
    color: #038252;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
}

/* Helpful Links Page Styles */
.helpful-links-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.links-header {
    background: linear-gradient(135deg, #00ab6b, #038252);
    color: white;
    padding: 3rem 2rem;
    border-radius: 12px;
    margin-bottom: 3rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 171, 107, 0.2);
}

.links-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.links-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.links-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.category-section {
    background: #fff;
    border: 1px solid #e3edf3;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.category-title {
    color: #00ab6b;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #00ab6b;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.category-title i {
    font-size: 1.5rem;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.link-item {
    background: #f8f9fa;
    border: 1px solid #e3edf3;
    border-radius: 8px;
    padding: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.link-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.link-item h4 {
    margin-bottom: 0.5rem;
}

.link-item h4 a {
    color: #00ab6b;
    text-decoration: none;
    font-weight: 600;
}

.link-item h4 a:hover {
    color: #038252;
    text-decoration: underline;
}

.link-item p {
    color: #666;
    margin-bottom: 0;
    line-height: 1.5;
}

/* Responsive design */
@media (max-width: 768px) {
    .current-projects-container {
        padding: 1rem 0.5rem;
    }

    .projects-header {
        padding: 1.5rem;
    }

    .projects-header .date-range {
        font-size: 1.1rem;
    }

    .day-section {
        padding-left: 1rem;
        border-left-width: 3px;
    }

    .day-title {
        font-size: 1.5rem;
    }

    .project-item {
        padding: 1.5rem;
    }

    .project-title {
        font-size: 1.2rem;
    }

    .project-date {
        font-size: 0.8rem;
        padding: 0.2rem 0.6rem;
    }

    /* Helpful Links responsive */
    .helpful-links-container {
        padding: 1rem 0.5rem;
    }

    .links-header {
        padding: 2rem 1.5rem;
    }

    .links-header h1 {
        font-size: 2rem;
    }

    .links-header p {
        font-size: 1rem;
    }

    .category-section {
        padding: 1.5rem;
    }

    .category-title {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .links-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .link-item {
        padding: 1rem;
    }
}

/* Cybersecurity Hardware Page Styles */
.hardware-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.hardware-header {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 3rem 2rem;
    border-radius: 12px;
    margin-bottom: 3rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

.hardware-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hardware-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.hardware-content {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

.hardware-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.hardware-item {
    background: #fff;
    border: 1px solid #e3edf3;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hardware-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hardware-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hardware-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.hardware-item:hover .hardware-image img {
    transform: scale(1.05);
}

.hardware-info {
    padding: 1.5rem;
}

.hardware-title {
    color: #dc3545;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    border-bottom: 2px solid #dc3545;
    padding-bottom: 0.5rem;
}

.hardware-description {
    margin-bottom: 1.5rem;
}

.hardware-description p {
    color: #333;
    line-height: 1.6;
    margin-bottom: 0;
}

.hardware-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.buy-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: #dc3545;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.buy-link:hover {
    background: #c82333;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.buy-link.secondary {
    background: #6c757d;
}

.buy-link.secondary:hover {
    background: #5a6268;
}

/* Hardware page responsive styles */
@media (max-width: 768px) {
    .hardware-container {
        padding: 1rem 0.5rem;
    }

    .hardware-header {
        padding: 2rem 1.5rem;
    }

    .hardware-header h1 {
        font-size: 2rem;
    }

    .hardware-header p {
        font-size: 1rem;
    }

    .hardware-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .hardware-image {
        height: 200px;
    }

    .hardware-info {
        padding: 1rem;
    }

    .hardware-title {
        font-size: 1.2rem;
    }

    .hardware-links {
        flex-direction: column;
    }

    .buy-link {
        justify-content: center;
        padding: 0.6rem 1rem;
    }
}

// Import partials from `sass_dir` (defaults to `_sass`)
@import
	"syntax",
    "starsnonscss"
;
