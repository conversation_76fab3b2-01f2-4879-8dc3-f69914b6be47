---
title: "Categories"
layout: default
permalink: "/categories.html"
---

<div class="row listrecent">
{% for category in site.categories %}
<div class="section-title col-md-12 mt-4">
<h2 id="{{ category[0] | replace: " ","-" }}">Category <span class="text-capitalize">{{ category[0] }}</span></h2>
</div>
{% assign pages_list = category[1] %}
{% for post in pages_list %}
{% if post.title != null %}
{% if group == null or group == post.group %}
{% include postbox.html %}
{% endif %}
{% endif %}
{% endfor %}
{% assign pages_list = nil %}
{% assign group = nil %}
{% endfor %}
</div>