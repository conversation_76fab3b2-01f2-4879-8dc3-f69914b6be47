---
title: "Tags"
layout: default
permalink: "/tags.html"
---

<div class="row listrecent">
{% for tag in site.tags %}
<div class="section-title col-md-12 mt-4">
<h2 id="{{ tag[0] | replace: " ","-" }}">Tag <span class="text-capitalize">{{ tag[0] }}</span></h2>
</div>
{% assign pages_list = tag[1] %}
{% for post in pages_list %}
{% if post.title != null %}
{% if group == null or group == post.group %}
{% include postbox.html %}
{% endif %}
{% endif %}
{% endfor %}
{% assign pages_list = nil %}
{% assign group = nil %}
{% endfor %}
</div>