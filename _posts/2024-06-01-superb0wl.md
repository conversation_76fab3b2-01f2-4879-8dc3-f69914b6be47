---
layout: post
title: "Unleashing SuperbØwl_v2: A WiFi-Controlled IR Botnet"
date: 2024-06-01
categories: [Cybersecurity, Coding Projects]
tags: [portfolio, coding projects, cybersecurity]
author: chris
image: assets/images/owl.jpg
---

**SuperbØwl: A Botnet for Killing TVs During the Super Bowl**

Back in 2020, I built **SuperbØwl**—a small device that uses an ESP8266 to remotely turn off TVs via infrared (IR). Originally a prank to kill the mood during the Super Bowl, it’s grown into a self-replicating IR botnet.

### What It Does

- **IR Blaster**: Sends IR power commands to nearby TVs.
- **WiFi Trigger**: Scans for specific WiFi networks, then activates.
- **Self-Replicating**: Once triggered, each device spoofs the original network and rebroadcasts, creating a chain reaction across devices.
- **Web Interface**: Configure IR codes and behavior through a simple browser UI.

### Botnet Behavior

Deploy a few across a bar, neighborhood, or office, and they’ll coordinate. One trigger and the whole swarm shuts down TVs in sync. No manual control needed after setup.

### Use Cases

- Kill TVs at sports bars during major games.
- Quiet down waiting rooms or lobbies.
- Or automate dumb devices with IR control.

### Get Started

Grab an ESP8266, follow the setup in the [GitHub repo](#), and flash the firmware. 


