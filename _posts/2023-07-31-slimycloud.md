---
featured: true
layout: post
title: Fun With LLMs - slimy.cloud
author: chris
date: 2023-01-07 14:30:00 -0500
categories: [AI, Coding Projects]
tags: [portfolio, coding projects, python, gpt, ai, flask]     # TAG names should always be lowercase
image: assets/images/54.png
---

<H2>SLIMY.CLOUD</H2>
This was the first AI / LLM app I created. Its purpose was to:
- Provide GPT access to friends and family without needing to create an account 
- Showcase the functionality of different system prompts (with the ability to stack multiple)
- Showcase GPT in multiple "rolls" : <PERSON><PERSON><PERSON>, IT Technician, Language Tutor 
  
Try it out at <https://slimy.cloud>  

  
<H2>Technology Used</H2>

<h3>Back End</h3>

- Python
- OpenAI API
- Flask

<h3>Front End</h3>

- html
- css
- javascript

<h3>Hosting</h3>

- Server hosted with Linode
- Ubuntu Linux
- Gunicorn
- Nginx
- SSL & DDOS protection through Cloudflare
