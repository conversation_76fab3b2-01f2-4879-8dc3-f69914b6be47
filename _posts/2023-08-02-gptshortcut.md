---
layout: post
title: Making Siri A Beast By Integrating Siri Shortcuts With ChatGPT
author: chris
date: 2023-08-02 14:30:00 -0500
categories: [AI, Coding Projects]
tags: [portfolio, coding projects, ai, mac, ios]     # TAG names should always be lowercase
image: assets/images/61.png
---

# Siri Shortcut for ChatGPT

I created this Siri shortcut for iOS to integrate the capabilities of ChatGPT into Siri, giving me the ability to use ChatGPT via speach input and receive a spoken response.
Once installed on an iOS device, you will be able to use the shortcut with any of your Mac devices including the HomePod.

# How To Use

- Install the official ChatGPT app from the App Store.
- Log in or create an account in ChatGPT.
- Install the iOS shortcut using the following this [**Link**](https://www.icloud.com/shortcuts/4ae3304051fd4b69b46622eea0661653)
- The name of the shortcut is how it will be triggered. It is currently named 'AI Mode' so to access, say 'Hey Siri, AI Mode' and it will execute the shortcut.
- It then prompts if you would like to continue your conversation or start a new chat.
- All conversations are written back to the ChatGPT app.

  
# Technology Used

- iOS
- Siri Shortcuts
- ChatGPT 
