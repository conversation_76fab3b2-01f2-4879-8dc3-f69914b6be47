---
featured: false
title: Using ChatGP<PERSON> and <PERSON><PERSON> As A Language Teacher
author: chris
date: 2023-07-29 14:30:00 -0500
categories: [Coding Projects]
tags: [portfolio, coding projects, ai, mac, ios, apple]    # TAG names should always be lowercase
image: assets/images/72.png
---

# Siri Shortcuts and ChatGPT As A Language Learning Tool

I created this Siri shortcut for iOS to integrate the capabilities of ChatGPT into Siri, giving me the ability to use ChatGPT via speach input and receive a spoken response.
This can be installed on an iOS device, and will work with your Mac devices including the Apple Watch, HomePod and HomePod Mini. 

To further expand on this concept, I wanted to build a task specific Siri Shortcut that included a system prompt to shape the response generated.

Using a similar prompt to the one I have set up on [**SLIMY.CLOUD**](https://slimy.cloud), I created this shortcut to assist in learning Brazillian Portuguese.


<img width="350" src="https://github.com/ECTO-1A/ECTO-1A.github.io/assets/*********/6e478ce1-2c56-44a2-87b1-40504600121c">


# How To Use

- Install the official ChatGPT app from the iOS App Store.
- Log in or create an account in ChatGPT.
- Install the iOS shortcut using the following [**Link**](https://www.icloud.com/shortcuts/8ab63f09918249a6b86633a27d495d11)
- The name of the shortcut is how it will be triggered. It is currently named 'Mano' so to access, say 'Hey Siri, Mano' and it will execute the shortcut. This can be changed by editing the name of the Shortcut in the Shortcuts App.
- It then prompts if you would like to continue your conversation or start a new chat.
- All conversations are written back to the ChatGPT app.

  
# Technology Used

- iOS
- Siri Shortcuts
- ChatGPT 
