---
featured: true
layout: post
title: ConTroll - GPT AI Prompt Injection Game
author: chris
date: 2023-04-30 14:00:00 -0500
categories: [AI, Cybersecurity, Coding Projects]
tags: [portfolio, coding projects, python, gpt, ai, flask, cybersecurity]     # TAG names should always be lowercase
image: assets/images/56.png
---

# ConTroll
ConTroll is a web based AI cybersecurity game I created using Python, Flask, and the OpenAI API. The game is built like all OpenAI based chat-bots and has a hidden system prompt. Your goal is to enter something into the chat input field that will convince OpenAI to respond in a way that reveals the password hidden in the system prompt.  
  
Each level has a different system prompt that uses various natural language methods to protect the information in that prompt.  
  
Try it out at <https://controll.cloud>  
  
![ConTroll](/assets/controll.png){: style="border: 3px solid black"}

<br/><br/>

# Technology Used

<h2>Back End</h2>

- Python
- OpenAI API
- Flask

<h2>Front End</h2>

- html
- css
- javascript

<h2>Hosting</h2>

- Cloud Hosting with Linode
- Ubuntu Linux
- Gunicorn
- Nginx
