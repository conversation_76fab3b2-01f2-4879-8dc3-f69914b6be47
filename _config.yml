# Site
name: "<PERSON>"
title: "<PERSON>"
description: "An ongoing brain dump of different coding, cybersecurity, and technology projects I'm working on."
logo: 'assets/images/log.png'
favicon: 'assets/images/favicon.png'
baseurl: /
google_analytics: '***********-1'
disqus: 'demowebsite'
include: ["_pages"]
permalink: /:title/

# Authors
authors:
  chris:
    name: <PERSON>
    display_name: ECTO-1A
    gravatar: e56154546cf4be74e393c62d1ae9f9d4
    email: <EMAIL>
    web: https://github.com/ECTO-1A
    twitter: https://x.com/ECTO_1A_
    description: "I'm an anomaly. An Enigma. A mystery wrapped in a riddle."
    avatar: 'assets/images/avatar.png'
  
# Plugins
plugins:
  - jekyll-paginate
  - jekyll-sitemap
  - jekyll-feed
  - jekyll-seo-tag
  - jekyll-archives
    
# Archives
jekyll-archives:
  enabled:
    - categories
  layout: archive
  permalinks:
    category: '/category/:name/'
    
# Pagination 
paginate: 6
paginate_path: /page:num/
    
# Other
markdown: kramdown

kramdown:
  input: GFM
  syntax_highlighter: rouge
  syntax_highlighter_opts:
    block:
      line_numbers: true

# Adsense (change to "enabled" to activate, also your client id and ad slot. Create a new ad unit from your Adsense account to get the slot.)
adsense: "disabled"
adsense-data-ad-client: "ca-pub-****************"
adsense-data-ad-slot: "**********"

# Lazy Images ("enabled" or "disabled")
lazyimages: "disabled"

exclude: [changelog.md, LICENSE.txt, README.md, Gemfile, Gemfile.lock]
